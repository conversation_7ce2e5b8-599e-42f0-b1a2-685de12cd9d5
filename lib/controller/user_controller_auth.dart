// lib/controller/user_controller_auth.dart

import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../services/klaviyo_service.dart';
import '../services/enhanced_email_verification_service.dart';
import '../services/comprehensive_logging_service.dart';
import './user_controller_base.dart';

/// Mixin that adds authentication functionality to UserController
mixin UserControllerAuth on UserControllerBase {
  static const _secureStorage = FlutterSecureStorage();

  // Error tracking for better user feedback
  String? _lastError;
  @override
  String? get error => _lastError;

  void _setError(String error) {
    _lastError = error;
    if (kDebugMode) print('❌ UserControllerAuth: Error set: $error');
  }

  void _clearError() {
    _lastError = null;
  }
  
  // Secure storage keys
  static const String _usernameKey = 'user_username';
  static const String _passwordHashKey = 'user_password_hash';
  static const String _emailKey = 'user_email';
  static const String _isEmailVerifiedKey = 'user_email_verified';
  static const String _klaviyoSubscribedKey = 'user_klaviyo_subscribed';
  static const String _assignedCoachesKey = 'user_assigned_coaches';

  /// Hash a password using SHA-256 (placeholder for bcrypt)
  /// TODO: Replace with proper bcrypt implementation when backend is ready
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Generate random coach assignments for non-gender users
  Map<String, String> _generateRandomCoachAssignments() {
    final random = Random();
    final categories = ['Health', 'Wealth', 'Purpose', 'Connection'];
    final assignments = <String, String>{};
    
    for (final category in categories) {
      // 50/50 split between male and female coaches
      final isMale = random.nextBool();
      assignments[category] = isMale ? 'Male' : 'Female';
    }
    
    // Handle custom categories if they exist
    if (user?.customCategories.isNotEmpty == true) {
      for (int i = 0; i < user!.customCategories.length; i++) {
        final categoryName = user!.customCategories[i];
        final isMale = random.nextBool();
        assignments[categoryName] = isMale ? 'Male' : 'Female';
      }
    }
    
    return assignments;
  }

  /// Create a new user account with username, email, and password
  Future<bool> createAccount({
    required String username,
    required String email,
    required String password,
    required String gender,
  }) async {
    _clearError(); // Clear any previous errors

    // Log signup attempt for monitoring
    await ComprehensiveLoggingService.logInfo('🚀 SIGNUP_ATTEMPT: Starting account creation for username: $username');
    final signupStartTime = DateTime.now();

    try {
      if (kDebugMode) print('🔄 UserControllerAuth: Starting createAccount for username: $username');

      // Clear any existing auth data first to prevent conflicts
      if (kDebugMode) print('🔄 UserControllerAuth: Clearing existing auth data...');
      await clearAuthData();

      // Hash the password
      final passwordHash = _hashPassword(password);
      if (kDebugMode) print('🔄 UserControllerAuth: Password hashed successfully');

      // Generate coach assignments for non-gender users
      Map<String, String>? assignedCoaches;
      if (gender == 'Non-Gender') {
        assignedCoaches = _generateRandomCoachAssignments();
        if (kDebugMode) print('🔄 UserControllerAuth: Generated random coach assignments');
      }

      // CRITICAL: Verify email is already in Klaviyo (should have been added by modal)
      if (kDebugMode) print('🔄 UserControllerAuth: Verifying email in Klaviyo...');
      final emailExists = await KlaviyoService.emailExists(email);
      if (!emailExists) {
        _setError('Email registration failed. Please try the signup process again.');
        if (kDebugMode) print('❌ UserControllerAuth: Email not found in Klaviyo - signup flow error');
        return false;
      }
      if (kDebugMode) print('✅ UserControllerAuth: Email verified in Klaviyo');

      // Create new user with authentication data
      final newUser = User.blank(id: username, username: username).copyWith(
        email: email,
        passwordHash: passwordHash,
        gender: gender,
        isEmailVerified: false,
        klaviyoSubscribed: true, // Set to true since email is already in Klaviyo
        assignedCoaches: assignedCoaches,
      );
      if (kDebugMode) print('🔄 UserControllerAuth: Created new user object');

      // Store sensitive data in secure storage with transaction safety
      if (kDebugMode) print('🔄 UserControllerAuth: Storing secure data with transaction safety...');
      try {
        // Store secure data first
        await _storeSecureData(username, passwordHash, email, false, true, assignedCoaches);
        if (kDebugMode) print('✅ UserControllerAuth: Secure data stored successfully');

        // Save user data
        await updateUser(newUser);
        if (kDebugMode) print('✅ UserControllerAuth: User data saved successfully');

      } catch (e) {
        _setError('Failed to save account data. Please try again.');
        if (kDebugMode) print('❌ UserControllerAuth: Storage operation failed: $e');
        // Clean up any partial data
        try {
          await clearAuthData();
        } catch (cleanupError) {
          if (kDebugMode) print('❌ UserControllerAuth: Cleanup failed: $cleanupError');
        }
        return false;
      }

      // Send email verification
      if (kDebugMode) print('🔄 UserControllerAuth: Sending email verification...');
      try {
        final emailService = EnhancedEmailVerificationService();
        await emailService.initialize();

        final verificationResult = await emailService.sendVerificationEmail(
          email: email,
          username: username,
          metadata: {
            'signup_timestamp': DateTime.now().toIso8601String(),
            'gender': gender,
            'source': 'account_creation',
          },
        );

        if (verificationResult.success) {
          if (kDebugMode) print('✅ UserControllerAuth: Email verification sent successfully');
        } else {
          if (kDebugMode) print('⚠️ UserControllerAuth: Email verification failed but continuing: ${verificationResult.message}');
          // Don't fail account creation if email verification fails
        }
      } catch (e) {
        if (kDebugMode) print('⚠️ UserControllerAuth: Email verification error but continuing: $e');
        // Don't fail account creation if email verification fails
      }

      // Log successful signup for monitoring
      final signupDuration = DateTime.now().difference(signupStartTime);
      await ComprehensiveLoggingService.logInfo('✅ SIGNUP_SUCCESS: Account created successfully for $username in ${signupDuration.inMilliseconds}ms');

      if (kDebugMode) print('✅ UserControllerAuth: Account creation completed successfully');
      return true;

    } catch (e) {
      // Log failed signup for monitoring
      final signupDuration = DateTime.now().difference(signupStartTime);
      await ComprehensiveLoggingService.logError('❌ SIGNUP_FAILURE: Account creation failed for $username after ${signupDuration.inMilliseconds}ms - Error: $e');

      _setError('Account creation failed. Please check your connection and try again.');
      if (kDebugMode) print('❌ UserControllerAuth: Failed to create account: $e');
      return false;
    }
  }

  /// Sign in with username and password
  Future<bool> signIn({
    required String username,
    required String password,
    bool skipPasswordCheck = false,
  }) async {
    try {
      // Try to load user from storage
      await refreshFromDisk();

      // Check if user exists and has the right username
      if (user == null || user!.username != username) {
        if (kDebugMode) print('User not found: $username');
        return false;
      }

      // Verify password (unless skipping for magic link authentication)
      if (!skipPasswordCheck) {
        final storedPasswordHash = await _secureStorage.read(key: _passwordHashKey);
        final inputPasswordHash = _hashPassword(password);

        if (storedPasswordHash != inputPasswordHash) {
          if (kDebugMode) print('Invalid password for user: $username');
          return false;
        }
      } else {
        if (kDebugMode) print('Skipping password check for magic link authentication');
      }

      // Load auth data
      await loadAuthData();

      return true;

    } catch (e) {
      if (kDebugMode) print('Sign in failed: $e');
      return false;
    }
  }

  /// Update user's email and sync with Klaviyo
  Future<bool> updateEmail(String newEmail) async {
    if (user == null) return false;

    try {
      // Check if email already exists in Klaviyo
      final emailExists = await KlaviyoService.emailExists(newEmail);
      if (emailExists) {
        if (kDebugMode) print('Email already exists');
        return false;
      }

      // Add to Klaviyo
      final success = await KlaviyoService.addEmailToList(newEmail);
      if (!success) {
        if (kDebugMode) print('Failed to update email');
        return false;
      }

      // Update user
      final updatedUser = user!.copyWith(
        email: newEmail,
        isEmailVerified: false,
        klaviyoSubscribed: true,
      );

      await updateUser(updatedUser);
      await _secureStorage.write(key: _emailKey, value: newEmail);
      await _secureStorage.write(key: _isEmailVerifiedKey, value: 'false');
      await _secureStorage.write(key: _klaviyoSubscribedKey, value: 'true');

      return true;

    } catch (e) {
      if (kDebugMode) print('Failed to update email: $e');
      return false;
    }
  }

  /// Update user's password
  Future<bool> updatePassword(String currentPassword, String newPassword) async {
    if (user == null) return false;

    try {
      // Verify current password
      final storedPasswordHash = await _secureStorage.read(key: _passwordHashKey);
      final currentPasswordHash = _hashPassword(currentPassword);

      if (storedPasswordHash != currentPasswordHash) {
        if (kDebugMode) print('Current password is incorrect');
        return false;
      }

      // Hash new password
      final newPasswordHash = _hashPassword(newPassword);

      // Update user
      final updatedUser = user!.copyWith(passwordHash: newPasswordHash);
      await updateUser(updatedUser);
      await _secureStorage.write(key: _passwordHashKey, value: newPasswordHash);

      return true;

    } catch (e) {
      if (kDebugMode) print('Failed to update password: $e');
      return false;
    }
  }

  /// Create user account without email requirement
  Future<bool> createUserWithoutEmail({
    required String username,
    required String gender,
    Map<String, String>? assignedCoaches,
  }) async {
    try {
      if (kDebugMode) print('🚀 UserControllerAuth: Creating email-less user: $username');

      // Create new user without email/password requirements
      final newUser = User.blank(id: username, username: username).copyWith(
        gender: gender,
        email: null,
        passwordHash: null,
        isEmailVerified: false,
        klaviyoSubscribed: false, // Skip Klaviyo for email-less users
        assignedCoaches: assignedCoaches,
      );

      // Store minimal secure data (no email/password)
      await _storeSecureDataEmailLess(username, assignedCoaches);

      // Update user through controller
      await updateUser(newUser);

      if (kDebugMode) print('✅ UserControllerAuth: Email-less user created successfully');
      return true;

    } catch (e) {
      if (kDebugMode) print('❌ UserControllerAuth: Failed to create email-less user: $e');
      return false;
    }
  }

  /// Store secure data for email-less users
  Future<void> _storeSecureDataEmailLess(
    String username,
    Map<String, String>? assignedCoaches,
  ) async {
    await _secureStorage.write(key: _usernameKey, value: username);
    // Skip email and password storage for email-less users
    await _secureStorage.write(key: _isEmailVerifiedKey, value: 'false');
    await _secureStorage.write(key: _klaviyoSubscribedKey, value: 'false');

    if (assignedCoaches != null) {
      await _secureStorage.write(
        key: 'user_assigned_coaches',
        value: jsonEncode(assignedCoaches),
      );
    }
  }

  /// Store sensitive authentication data in secure storage
  Future<void> _storeSecureData(
    String username,
    String passwordHash,
    String email,
    bool isEmailVerified,
    bool klaviyoSubscribed,
    Map<String, String>? assignedCoaches,
  ) async {
    await _secureStorage.write(key: _usernameKey, value: username);
    await _secureStorage.write(key: _passwordHashKey, value: passwordHash);
    await _secureStorage.write(key: _emailKey, value: email);
    await _secureStorage.write(key: _isEmailVerifiedKey, value: isEmailVerified.toString());
    await _secureStorage.write(key: _klaviyoSubscribedKey, value: klaviyoSubscribed.toString());
    
    if (assignedCoaches != null) {
      final assignedCoachesJson = jsonEncode(assignedCoaches);
      await _secureStorage.write(key: _assignedCoachesKey, value: assignedCoachesJson);
    }
  }

  /// Load authentication data from secure storage
  Future<void> loadAuthData() async {
    if (user == null) return;
    
    try {
      final email = await _secureStorage.read(key: _emailKey);
      final isEmailVerifiedStr = await _secureStorage.read(key: _isEmailVerifiedKey);
      final klaviyoSubscribedStr = await _secureStorage.read(key: _klaviyoSubscribedKey);
      final assignedCoachesStr = await _secureStorage.read(key: _assignedCoachesKey);
      
      Map<String, String>? assignedCoaches;
      if (assignedCoachesStr != null) {
        assignedCoaches = Map<String, String>.from(jsonDecode(assignedCoachesStr));
      }
      
      final updatedUser = user!.copyWith(
        email: email,
        isEmailVerified: isEmailVerifiedStr == 'true',
        klaviyoSubscribed: klaviyoSubscribedStr == 'true',
        assignedCoaches: assignedCoaches,
      );
      
      await updateUser(updatedUser);
      
    } catch (e) {
      // Handle error silently - auth data might not exist yet
      if (kDebugMode) print('Failed to load auth data: $e');
    }
  }

  /// Clear all authentication data (for logout)
  Future<void> clearAuthData() async {
    await _secureStorage.delete(key: _usernameKey);
    await _secureStorage.delete(key: _passwordHashKey);
    await _secureStorage.delete(key: _emailKey);
    await _secureStorage.delete(key: _isEmailVerifiedKey);
    await _secureStorage.delete(key: _klaviyoSubscribedKey);
    await _secureStorage.delete(key: _assignedCoachesKey);
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final username = await _secureStorage.read(key: _usernameKey);
    final passwordHash = await _secureStorage.read(key: _passwordHashKey);
    return username != null && passwordHash != null;
  }

  /// Get stored username
  Future<String?> getStoredUsername() async {
    return await _secureStorage.read(key: _usernameKey);
  }
}
