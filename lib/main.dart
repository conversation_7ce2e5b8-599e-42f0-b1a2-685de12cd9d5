// 📁 lib/main.dart

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';

// Core models
import 'models/user_model.dart';
import 'models/bounty_model.dart';

// Services
import 'services/user_service.dart';
import 'services/music_service.dart';
import 'services/reward_engine.dart';
import 'services/smart_service_manager.dart';
import 'services/release_config_service.dart';

// Controllers
import 'controller/user_controller2.dart';
import 'controller/reward_controller.dart';
import 'controller/firebase_auth_controller.dart';

// Notifications and widgets
import 'notify/notify_bridge.dart';
import 'notify/widget_controller.dart';

// Core systems
import 'bulletproof/error_handler.dart';
import 'performance/performance_dashboard.dart';

// UI components
import 'screens/auth_screen.dart';
import 'screens/home_screen.dart';
import 'theme/theme_provider.dart';
import 'theme/colors.dart';

// Initialization modules
import 'core/app_initializer.dart';
import 'widgets/rewards_dashboard.dart';
import 'widgets/reward_popups.dart';
import 'widgets/cashed_bounties_modal.dart';


// Service initialization moved to AppInitializer

/// Main entry point for the Maxed Out Life application.
///
/// Initializes all core systems, services, and controllers before launching
/// the app. Includes comprehensive error handling and graceful degradation
/// for non-critical failures.
void main() async {
  try {
    // Use centralized app initialization with lazy loading
    final initSteps = await AppInitializer.completeInitialization();
    final errorHandler = AppInitializer.getErrorHandler() ?? ErrorHandler();

    // Start lazy loading of non-critical services in background
    AppInitializer.initializeNonCriticalServicesLazily();

  // ── Initialize core services ───────────────
  final musicService = MusicService();
  final userController = UserController2();
  final firebaseAuthController = FirebaseAuthController();

  // Initialize Firebase Auth Controller
  try {
    await firebaseAuthController.initialize();
    initSteps['firebase_auth'] = true;
    if (kDebugMode) debugPrint('✅ Firebase Auth Controller initialized');
  } catch (e) {
    initSteps['firebase_auth'] = false;
    debugPrint('⚠️ Failed to initialize Firebase Auth Controller (continuing with fallback): $e');
  }

  // ── 12) Load user from SharedPreferences ───────────────
  User? loadedUser;
  bool hasSavedUser = false;

  // DEBUG: Force onboarding flow to test custom categories fix
  const bool forceOnboarding = false; // Set to true to force onboarding flow

  if (!forceOnboarding) {
    try {
      final userService = UserService(errorHandler);
      final lastUsername = await userService.getLastUser();
      if (lastUsername != null) {
        loadedUser = await userService.loadUserByUsername(lastUsername);
        if (loadedUser != null) {
          userController.user = loadedUser;
          hasSavedUser = true;
          initSteps['user_load'] = true;
          if (kDebugMode) debugPrint('✅ User loaded: ${loadedUser.username}');
        }
      }
    } catch (e) {
      initSteps['user_load'] = false;
      debugPrint('⚠️ No existing user in SharedPreferences: $e');
    }
  }

  // ── 13) Initialize music service (lazy loading - no startup delay) ───────────────
  try {
    // Music service is now lazy-loaded, so this just creates the instance
    // Actual music will start when user first interacts with the app
    initSteps['music'] = true;
    if (kDebugMode) debugPrint('✅ Music service created (lazy loading)');

    // Start music in background after app is fully loaded
    Future.delayed(const Duration(milliseconds: 2000), () async {
      try {
        await musicService.initAndPlay();
        if (kDebugMode) debugPrint('🎵 Background music started (lazy loaded)');
      } catch (e) {
        debugPrint('⚠️ Failed to start background music (continuing without it): $e');
      }
    });
  } catch (e) {
    initSteps['music'] = false;
    debugPrint('⚠️ Failed to create music service (continuing without it): $e');
  }

  // ── 7) Initialize NotifyBridge for notifications ───────────────
  try {
    await NotifyBridge().initialize();
    initSteps['notifications'] = true;
    if (kDebugMode) debugPrint('✅ Notifications initialized');
  } catch (e) {
    initSteps['notifications'] = false;
    // Don't let notification failure crash the app
    debugPrint('⚠️ Failed to initialize notifications (continuing without them): $e');
  }

  // ── 7.5) Deep Link Handler managed by Smart Service Manager ───────────────
  // Smart Service Manager will handle deep link initialization
  initSteps['deep_links'] = true;
  debugPrint('🧠 Deep link handler will be managed by Smart Service Manager');

  // ── 8) Initialize widget controller ───────────────
  final widgetController = WidgetController(userController, errorHandler);
  try {
    await widgetController.syncWithUserState();
    initSteps['widgets'] = true;
    if (kDebugMode) debugPrint('✅ Widget controller initialized');
  } catch (e) {
    initSteps['widgets'] = false;
    debugPrint('⚠️ Failed to initialize widgets (continuing without them): $e');
  }

  // ── 9) Initialize reward engine ───────────────
  try {
    await RewardEngine.instance.initialize();
    initSteps['rewards'] = true;
    if (kDebugMode) debugPrint('✅ Reward engine initialized');
  } catch (e) {
    initSteps['rewards'] = false;
    debugPrint('⚠️ Failed to initialize reward engine (continuing without it): $e');
  }

  // ── 10) Log initialization summary ───────────────
  if (kDebugMode) {
    final successCount = initSteps.values.where((success) => success).length;
    final totalCount = initSteps.length;
    debugPrint('🚀 App initialization complete: $successCount/$totalCount systems initialized');

    if (successCount < totalCount) {
      final failedSystems = initSteps.entries
          .where((entry) => !entry.value)
          .map((entry) => entry.key)
          .join(', ');
      debugPrint('⚠️ Failed systems: $failedSystems');
    }
  }

  // ── 11) Launch app with all providers ───────────────
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<UserController2>.value(value: userController),
        ChangeNotifierProvider<FirebaseAuthController>.value(value: firebaseAuthController),
        ChangeNotifierProvider<MusicService>.value(value: musicService),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        Provider<WidgetController>.value(value: widgetController),
        ChangeNotifierProvider(create: (_) => RewardController()..initialize()),
        Provider<ErrorHandler>.value(value: errorHandler),
      ],
      child: MaxedOutLifeApp(
        hasSavedUser: hasSavedUser,
        loadedUser: loadedUser,
        initializationStatus: initSteps,
      ),
    ),
  );
  } catch (e, stackTrace) {
    // Last resort error handling - if anything fails catastrophically,
    // still try to launch a minimal app
    debugPrint('🚨 CRITICAL ERROR in main(): $e');
    debugPrint('Stack trace: $stackTrace');

    // Launch minimal app without any initialization
    runApp(
      MaterialApp(
        title: 'Maxed Out Life',
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          backgroundColor: Colors.black,
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 64),
                const SizedBox(height: 16),
                const Text(
                  'App initialization failed',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Please restart the app',
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Main application widget that provides the MaterialApp and theme configuration.
///
/// Accepts initialization status to provide debugging information and graceful
/// degradation when certain systems fail to initialize.
class MaxedOutLifeApp extends StatefulWidget {
  /// Indicates whether we successfully loaded a user during main()
  final bool hasSavedUser;

  /// The loaded user data, if any
  final User? loadedUser;

  /// Status of each initialization step for debugging
  final Map<String, bool> initializationStatus;

  const MaxedOutLifeApp({
    super.key,
    required this.hasSavedUser,
    this.loadedUser,
    this.initializationStatus = const {},
  });

  @override
  State<MaxedOutLifeApp> createState() => _MaxedOutLifeAppState();
}

class _MaxedOutLifeAppState extends State<MaxedOutLifeApp> with WidgetsBindingObserver {
  ThemeMode _themeMode = ThemeMode.dark;

  @override
  void initState() {
    super.initState();

    // Add lifecycle observer to handle app state changes
    WidgetsBinding.instance.addObserver(this);
    _initializeAppState();
    _logInitializationStatus();
  }

  /// Initialize app-level state including theme preferences
  Future<void> _initializeAppState() async {
    try {
      // Load saved theme mode from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString('themeMode') ?? 'dark';

      if (mounted) {
        setState(() {
          _themeMode = (savedTheme == 'light') ? ThemeMode.light : ThemeMode.dark;
        });
      }

      if (kDebugMode) {
        debugPrint('✅ App state initialized with theme: $savedTheme');
      }

      // Start Smart Service Manager background initialization after UI is ready
      _initializeBackgroundServices();
    } catch (e, st) {
      // Handle error without using BuildContext across async gap
      debugPrint('❌ Failed to initialize app state: $e');
      if (kDebugMode) {
        debugPrint('Stack trace: $st');
      }
    }
  }

  /// Log initialization status for debugging
  void _logInitializationStatus() {
    if (kDebugMode && widget.initializationStatus.isNotEmpty) {
      final successCount = widget.initializationStatus.values.where((s) => s).length;
      final totalCount = widget.initializationStatus.length;
      debugPrint('📊 Initialization Status: $successCount/$totalCount systems ready');
    }
  }

  /// Initialize background services after UI is ready
  void _initializeBackgroundServices() {
    // Get user controller reference before async operation
    final userController = Provider.of<UserController2>(context, listen: false);
    final user = userController.user;

    // Start Smart Service Manager in background after a short delay
    Future.delayed(const Duration(milliseconds: 1000), () async {
      try {
        debugPrint('🧠 Starting Smart Service Manager background initialization...');
        SmartServiceManager.initializeInBackground(user: user);

        if (kDebugMode) {
          debugPrint('✅ Background services initialization started');
        }
      } catch (e) {
        debugPrint('⚠️ Failed to start background services: $e');
      }
    });
  }

  @override
  void dispose() {
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
        // App is backgrounded - pause resource-intensive operations
        _handleAppPaused();
        break;
      case AppLifecycleState.resumed:
        // App is foregrounded - resume operations
        _handleAppResumed();
        break;
      case AppLifecycleState.detached:
        // App is being terminated - cleanup resources
        _handleAppDetached();
        break;
      case AppLifecycleState.inactive:
        // App is inactive (e.g., phone call) - reduce activity
        break;
      case AppLifecycleState.hidden:
        // App is hidden - similar to paused
        _handleAppPaused();
        break;
    }
  }

  /// Handle app being paused/backgrounded
  void _handleAppPaused() {
    try {
      // Only call services if they were actually initialized
      // Note: These services were deferred during startup to prevent memory exhaustion

      if (kDebugMode) {
        debugPrint('📱 App paused - lightweight resource conservation');
      }
    } catch (e) {
      debugPrint('⚠️ Error handling app pause: $e');
    }
  }

  /// Handle app being resumed/foregrounded
  void _handleAppResumed() {
    try {
      // Only call services if they were actually initialized
      // Note: These services were deferred during startup to prevent memory exhaustion

      if (kDebugMode) {
        debugPrint('📱 App resumed - lightweight operations restored');
      }
    } catch (e) {
      debugPrint('⚠️ Error handling app resume: $e');
    }
  }

  /// Handle app being terminated
  void _handleAppDetached() {
    try {
      // Stop Smart Service Manager background initialization
      SmartServiceManager.stopBackgroundInitialization();

      if (kDebugMode) {
        debugPrint('📱 App detached - Smart Service Manager stopped');
      }
    } catch (e) {
      debugPrint('⚠️ Error handling app detach: $e');
    }
  }

  /// Show service status dialog for debugging
  void _showServiceStatusDialog(BuildContext context) {
    final status = SmartServiceManager.getServiceStatus();
    final summary = SmartServiceManager.getInitializationSummary();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🧠 Smart Service Manager'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(summary),
              const SizedBox(height: 16),
              Text('Initialized: ${status['successful_services']}/${status['initialized_services']}'),
              Text('Failed: ${status['failed_services']}'),
              Text('Is Initializing: ${status['is_initializing']}'),
              const SizedBox(height: 16),
              const Text('Service Details:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...status['service_details'].entries.map((entry) =>
                Text('${entry.key}: ${entry.value ? "✅" : "❌"}')
              ).toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final lightTheme = themeProvider.lightTheme;
    final darkTheme = themeProvider.darkTheme;

    return MaterialApp(
      title: 'Maxed Out Life',
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: _themeMode,
      debugShowCheckedModeBanner: false,
      home: Builder(
        builder: (context) {
          // Initialize Smart Service Manager after UI is ready
          WidgetsBinding.instance.addPostFrameCallback((_) {
            SmartServiceManager.initializeInBackground(user: widget.loadedUser);
          });

          return Stack(
            children: [
              // Show home screen if user is already loaded, otherwise show auth screen
              widget.hasSavedUser && widget.loadedUser != null
                ? Consumer<UserController2>(
                    builder: (context, userController, child) {
                      return HomeScreen(
                        user: widget.loadedUser!,
                        onUserUpdated: (updatedUser) {
                          userController.updateUser(updatedUser);
                        },
                        onSignOut: () {
                          // Clear user data and navigate to auth
                          userController.clearAuthData();
                          Navigator.of(context).pushAndRemoveUntil(
                            MaterialPageRoute(builder: (context) => const AuthScreen()),
                            (route) => false,
                          );
                        },
                        onReset: (user) {
                          userController.clearAuthData();
                        },
                        toggleTheme: () {
                          setState(() {
                            _themeMode = _themeMode == ThemeMode.dark
                                ? ThemeMode.light
                                : ThemeMode.dark;
                          });
                        },
                      );
                    },
                  )
                : const AuthScreen(),
            // Debug overlay for service status (hidden in release mode)
            if (ReleaseConfigService.shouldShowDebugOverlays)
              Positioned(
                top: 50,
                right: 10,
                child: Column(
                  children: [
                    FloatingActionButton(
                      heroTag: "performance_debug_fab",
                      mini: true,
                      backgroundColor: MolColors.cyan.withValues(alpha: 0.8),
                      onPressed: () => PerformanceDashboard.showOverlay(context),
                      child: const Icon(Icons.speed, color: Colors.black),
                    ),
                    const SizedBox(height: 8),
                    FloatingActionButton(
                      heroTag: "service_status_fab",
                      mini: true,
                      backgroundColor: MolColors.green.withValues(alpha: 0.8),
                      onPressed: () => _showServiceStatusDialog(context),
                      child: const Icon(Icons.settings, color: Colors.black),
                    ),
                  ],
                ),
              ),
          ],
          );
        },
      ),
    );
  }
}

class HomeScreen3WithRewards extends StatefulWidget {
  const HomeScreen3WithRewards({super.key});

  @override
  State<HomeScreen3WithRewards> createState() => _HomeScreen3WithRewardsState();
}

class _HomeScreen3WithRewardsState extends State<HomeScreen3WithRewards> {
  bool _showCashedBounties = false;
  String? _errorMessage;

  static final List<CashedBounty> _cashedBounties = [
    CashedBounty(
      bounty: BountyModel(
        id: 'b1',
        description: 'Go to a new fighting gym and train for two hours',
        categories: ['Health', 'Purpose'],
        expPerCategory: {'Health': 40, 'Purpose': 40},
        difficulty: 'hard',
        isEpic: false,
      ),
      photoPath: '', // Placeholder for photo path
      completedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    CashedBounty(
      bounty: BountyModel(
        id: 'b4',
        description: 'Try a new healthy recipe you\'ve never cooked before',
        categories: ['Health'],
        expPerCategory: {'Health': 20},
        difficulty: 'easy',
        isEpic: false,
      ),
      photoPath: '', // Placeholder for photo path
      completedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final rewardController = Provider.of<RewardController>(context, listen: false);
    final userController = Provider.of<UserController2>(context, listen: false);
    final glowColor = MolColors.purple;
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('MXD Out Life'),
      ),
      body: Stack(
        children: [
          Center(
            child: ValueListenableBuilder(
              valueListenable: rewardController.model,
              builder: (context, model, _) {
                // Example categories and custom categories for demo
                final categories = ['Health', 'Wealth', 'Purpose', 'Connection', 'Custom1', 'Custom2'];
                final customCategories = ['Custom1', 'Custom2'];

                // Get the current user from UserController2
                final currentUser = userController.user;
                if (currentUser == null) {
                  return const Center(
                    child: Text('No user loaded', style: TextStyle(color: Colors.white)),
                  );
                }

                return RewardsDashboard(
                  glowColor: glowColor,
                  categories: categories,
                  customCategories: customCategories,
                  todayBounty: RewardEngine.instance.activeBounty,
                  bonusExpToday: model.bonusExpToday,
                  user: currentUser,
                  onSpin: (updatedUser, result) {
                    try {
                      // Update the user with spinner results
                      userController.updateUser(updatedUser);
                      userController.save();

                      // Show result message
                      setState(() => _errorMessage = result.message);
                      Future.delayed(const Duration(seconds: 3), () {
                        if (mounted) setState(() => _errorMessage = null);
                      });
                    } catch (e) {
                      setState(() => _errorMessage = 'Failed to process spin result: $e');
                      Future.delayed(const Duration(seconds: 3), () {
                        if (mounted) setState(() => _errorMessage = null);
                      });
                    }
                  },
                  onCashedBounties: () {
                    setState(() => _showCashedBounties = true);
                  },
                );
              },
            ),
          ),
          RewardPopups(glowColor: glowColor),
          if (_showCashedBounties)
            Positioned.fill(
              child: Container(
                color: Colors.black.withAlpha(179),
                child: Center(
                  child: CashedBountiesModal(
                    bounties: _cashedBounties,
                    glowColor: glowColor,
                    onClose: () => setState(() => _showCashedBounties = false),
                  ),
                ),
              ),
            ),
          if (_errorMessage != null)
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

