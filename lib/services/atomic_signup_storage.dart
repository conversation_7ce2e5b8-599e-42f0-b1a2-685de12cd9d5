import 'dart:convert';
import '../models/user_model.dart';
import '../services/bulletproof_secure_storage.dart';
import '../services/comprehensive_logging_service.dart';
import '../controller/user_controller2.dart';

/// 🔐 Atomic Signup Storage Service
/// 
/// Provides bulletproof atomic transactions for signup data persistence.
/// Ensures all user data (username, email, password, player data) is saved
/// atomically - either all succeeds or all fails with automatic rollback.
/// 
/// Features:
/// - Atomic transactions with rollback capability
/// - Storage verification after each operation
/// - Automatic recovery from corrupted storage
/// - Offline queue management for failed operations
/// - Comprehensive error handling and logging
class AtomicSignupStorage {
  static final AtomicSignupStorage _instance = AtomicSignupStorage._internal();
  factory AtomicSignupStorage() => _instance;
  AtomicSignupStorage._internal();

  final BulletproofSecureStorage _secureStorage = BulletproofSecureStorage();
  bool _isInitialized = false;
  
  // Transaction state management
  bool _transactionInProgress = false;
  final Map<String, String?> _transactionBackup = {};
  
  // Storage keys
  static const String _transactionStateKey = 'atomic_transaction_state';
  static const String _backupDataKey = 'atomic_backup_data';
  static const String _offlineQueueKey = 'atomic_offline_queue';
  
  /// Initialize the atomic storage service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _secureStorage.initialize();
      
      // Check for incomplete transactions and recover
      await _recoverIncompleteTransactions();
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('🔐 AtomicSignupStorage initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize AtomicSignupStorage: $e');
      rethrow;
    }
  }

  /// Ensure the service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Execute atomic signup without email requirement
  Future<bool> executeAtomicSignupWithoutEmail({
    required String username,
    required String gender,
    Map<String, String>? assignedCoaches,
    required UserController2 userController,
  }) async {

    try {
      await ComprehensiveLoggingService.logInfo('🚀 Starting atomic email-less signup for: $username');

      // Start transaction
      await _startTransaction();

      // Create backup of current state
      await _createBackup();

      // Execute storage operations for email-less user
      final success = await _executeStorageOperationsEmailLess(
        username: username,
        gender: gender,
        assignedCoaches: assignedCoaches,
        userController: userController,
      );

      if (success) {
        await _commitTransaction();
        await ComprehensiveLoggingService.logInfo('✅ Atomic email-less signup completed successfully');
        return true;
      } else {
        await _rollbackTransaction();
        await ComprehensiveLoggingService.logError('❌ Atomic email-less signup failed - rolled back');
        return false;
      }

    } catch (e) {
      await _rollbackTransaction();
      await ComprehensiveLoggingService.logError('❌ Atomic email-less signup exception: $e');
      return false;
    }
  }

  /// Execute an atomic signup transaction
  Future<bool> executeAtomicSignup({
    required String username,
    required String email,
    required String passwordHash,
    required String gender,
    Map<String, String>? assignedCoaches,
    required UserController2 userController,
  }) async {
    await _ensureInitialized();
    
    if (_transactionInProgress) {
      await ComprehensiveLoggingService.logWarning('⚠️ Transaction already in progress');
      return false;
    }
    
    try {
      await ComprehensiveLoggingService.logInfo('🚀 Starting atomic signup transaction for: $username');
      
      // Start transaction
      await _startTransaction();
      
      // Create backup of current state
      await _createBackup();
      
      // Execute all storage operations atomically
      final success = await _executeStorageOperations(
        username: username,
        email: email,
        passwordHash: passwordHash,
        gender: gender,
        assignedCoaches: assignedCoaches,
        userController: userController,
      );
      
      if (success) {
        // Verify all data was stored correctly
        final verified = await _verifyStoredData(
          username: username,
          email: email,
          passwordHash: passwordHash,
          gender: gender,
          assignedCoaches: assignedCoaches,
        );
        
        if (verified) {
          await _commitTransaction();
          await ComprehensiveLoggingService.logInfo('✅ Atomic signup transaction completed successfully');
          return true;
        } else {
          await ComprehensiveLoggingService.logError('❌ Data verification failed, rolling back');
          await _rollbackTransaction();
          return false;
        }
      } else {
        await ComprehensiveLoggingService.logError('❌ Storage operations failed, rolling back');
        await _rollbackTransaction();
        return false;
      }
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Atomic signup transaction failed: $e');
      await _rollbackTransaction();
      return false;
    }
  }

  /// Start a new transaction
  Future<void> _startTransaction() async {
    _transactionInProgress = true;
    await _secureStorage.write(key: _transactionStateKey, value: 'in_progress');
  }

  /// Create backup of current state
  Future<void> _createBackup() async {
    _transactionBackup.clear();
    
    // Backup existing secure storage data
    final keys = [
      'user_username',
      'user_password_hash', 
      'user_email',
      'user_email_verified',
      'user_klaviyo_subscribed',
      'user_assigned_coaches',
    ];
    
    for (final key in keys) {
      final value = await _secureStorage.read(key: key);
      _transactionBackup[key] = value;
    }
    
    // Store backup
    await _secureStorage.write(
      key: _backupDataKey, 
      value: jsonEncode(_transactionBackup),
    );
  }

  /// Recover from incomplete transactions
  Future<void> _recoverIncompleteTransactions() async {
    try {
      final transactionState = await _secureStorage.read(key: _transactionStateKey);

      if (transactionState == 'in_progress') {
        await ComprehensiveLoggingService.logWarning('⚠️ Incomplete transaction detected, recovering...');
        await _rollbackTransaction();
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to recover incomplete transactions: $e');
    }
  }

  /// Execute all storage operations atomically
  Future<bool> _executeStorageOperations({
    required String username,
    required String email,
    required String passwordHash,
    required String gender,
    Map<String, String>? assignedCoaches,
    required UserController2 userController,
  }) async {
    try {
      // Store secure authentication data
      await _secureStorage.write(key: 'user_username', value: username);
      await _secureStorage.write(key: 'user_password_hash', value: passwordHash);
      await _secureStorage.write(key: 'user_email', value: email);
      await _secureStorage.write(key: 'user_email_verified', value: 'false');
      await _secureStorage.write(key: 'user_klaviyo_subscribed', value: 'true');

      if (assignedCoaches != null) {
        await _secureStorage.write(
          key: 'user_assigned_coaches',
          value: jsonEncode(assignedCoaches),
        );
      }

      // Create and store user object
      final newUser = User.blank(id: username, username: username).copyWith(
        email: email,
        passwordHash: passwordHash,
        gender: gender,
        isEmailVerified: false,
        klaviyoSubscribed: true,
        assignedCoaches: assignedCoaches,
      );

      // Update user through controller
      await userController.updateUser(newUser);

      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Storage operations failed: $e');
      return false;
    }
  }

  /// Execute storage operations for email-less users
  Future<bool> _executeStorageOperationsEmailLess({
    required String username,
    required String gender,
    Map<String, String>? assignedCoaches,
    required UserController2 userController,
  }) async {
    try {
      // Store minimal secure data (no email/password)
      await _secureStorage.write(key: 'user_username', value: username);
      await _secureStorage.write(key: 'user_email_verified', value: 'false');
      await _secureStorage.write(key: 'user_klaviyo_subscribed', value: 'false');

      if (assignedCoaches != null) {
        await _secureStorage.write(
          key: 'user_assigned_coaches',
          value: jsonEncode(assignedCoaches),
        );
      }

      // Create and store user object without email
      final newUser = User.blank(id: username, username: username).copyWith(
        gender: gender,
        email: null,
        passwordHash: null,
        isEmailVerified: false,
        klaviyoSubscribed: false,
        assignedCoaches: assignedCoaches,
      );

      // Update user through controller
      await userController.updateUser(newUser);

      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Email-less storage operations failed: $e');
      return false;
    }
  }

  /// Verify all data was stored correctly
  Future<bool> _verifyStoredData({
    required String username,
    required String email,
    required String passwordHash,
    required String gender,
    Map<String, String>? assignedCoaches,
  }) async {
    try {
      // Verify secure storage data
      final storedUsername = await _secureStorage.read(key: 'user_username');
      final storedPasswordHash = await _secureStorage.read(key: 'user_password_hash');
      final storedEmail = await _secureStorage.read(key: 'user_email');
      final storedEmailVerified = await _secureStorage.read(key: 'user_email_verified');
      final storedKlaviyoSubscribed = await _secureStorage.read(key: 'user_klaviyo_subscribed');

      if (storedUsername != username ||
          storedPasswordHash != passwordHash ||
          storedEmail != email ||
          storedEmailVerified != 'false' ||
          storedKlaviyoSubscribed != 'true') {
        return false;
      }

      // Verify assigned coaches if provided
      if (assignedCoaches != null) {
        final storedCoachesStr = await _secureStorage.read(key: 'user_assigned_coaches');
        if (storedCoachesStr == null) return false;

        final storedCoaches = Map<String, String>.from(jsonDecode(storedCoachesStr));
        if (!_mapsEqual(assignedCoaches, storedCoaches)) {
          return false;
        }
      }

      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Data verification failed: $e');
      return false;
    }
  }

  /// Helper method to compare maps
  bool _mapsEqual(Map<String, String> map1, Map<String, String> map2) {
    if (map1.length != map2.length) return false;

    for (final key in map1.keys) {
      if (map1[key] != map2[key]) return false;
    }

    return true;
  }

  /// Commit the transaction
  Future<void> _commitTransaction() async {
    _transactionInProgress = false;
    _transactionBackup.clear();

    await _secureStorage.delete(key: _transactionStateKey);
    await _secureStorage.delete(key: _backupDataKey);
  }

  /// Rollback the transaction
  Future<void> _rollbackTransaction() async {
    try {
      // Restore from backup
      final backupDataStr = await _secureStorage.read(key: _backupDataKey);
      if (backupDataStr != null) {
        final backupData = Map<String, String?>.from(jsonDecode(backupDataStr));

        for (final entry in backupData.entries) {
          if (entry.value != null) {
            await _secureStorage.write(key: entry.key, value: entry.value!);
          } else {
            await _secureStorage.delete(key: entry.key);
          }
        }
      }

      _transactionInProgress = false;
      _transactionBackup.clear();

      await _secureStorage.delete(key: _transactionStateKey);
      await _secureStorage.delete(key: _backupDataKey);

      await ComprehensiveLoggingService.logInfo('🔄 Transaction rolled back successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Rollback failed: $e');
    }
  }

  /// Add signup to offline queue
  Future<void> addToOfflineQueue({
    required String username,
    required String email,
    required String passwordHash,
    required String gender,
    Map<String, String>? assignedCoaches,
  }) async {
    await _ensureInitialized();

    try {
      final queueItem = {
        'username': username,
        'email': email,
        'passwordHash': passwordHash,
        'gender': gender,
        'assignedCoaches': assignedCoaches,
        'timestamp': DateTime.now().toIso8601String(),
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      // Get existing queue
      final queueStr = await _secureStorage.read(key: _offlineQueueKey);
      List<dynamic> queue = [];
      if (queueStr != null) {
        queue = jsonDecode(queueStr);
      }

      // Add new item
      queue.add(queueItem);

      // Store updated queue
      await _secureStorage.write(key: _offlineQueueKey, value: jsonEncode(queue));

      await ComprehensiveLoggingService.logInfo('📥 Added signup to offline queue: $username');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to add to offline queue: $e');
    }
  }

  /// Process offline queue
  Future<List<Map<String, dynamic>>> processOfflineQueue(UserController2 userController) async {
    await _ensureInitialized();

    final results = <Map<String, dynamic>>[];

    try {
      final queueStr = await _secureStorage.read(key: _offlineQueueKey);
      if (queueStr == null) return results;

      final queue = List<dynamic>.from(jsonDecode(queueStr));
      final processedItems = <int>[];

      for (int i = 0; i < queue.length; i++) {
        final item = Map<String, dynamic>.from(queue[i]);

        try {
          final success = await executeAtomicSignup(
            username: item['username'],
            email: item['email'],
            passwordHash: item['passwordHash'],
            gender: item['gender'],
            assignedCoaches: item['assignedCoaches'] != null
                ? Map<String, String>.from(item['assignedCoaches'])
                : null,
            userController: userController,
          );

          results.add({
            'id': item['id'],
            'username': item['username'],
            'success': success,
            'timestamp': item['timestamp'],
          });

          if (success) {
            processedItems.add(i);
          }
        } catch (e) {
          results.add({
            'id': item['id'],
            'username': item['username'],
            'success': false,
            'error': e.toString(),
            'timestamp': item['timestamp'],
          });
        }
      }

      // Remove successfully processed items
      if (processedItems.isNotEmpty) {
        for (int i = processedItems.length - 1; i >= 0; i--) {
          queue.removeAt(processedItems[i]);
        }

        if (queue.isEmpty) {
          await _secureStorage.delete(key: _offlineQueueKey);
        } else {
          await _secureStorage.write(key: _offlineQueueKey, value: jsonEncode(queue));
        }
      }

      await ComprehensiveLoggingService.logInfo('📤 Processed ${processedItems.length} items from offline queue');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to process offline queue: $e');
    }

    return results;
  }

  /// Get offline queue status
  Future<Map<String, dynamic>> getOfflineQueueStatus() async {
    await _ensureInitialized();

    try {
      final queueStr = await _secureStorage.read(key: _offlineQueueKey);
      if (queueStr == null) {
        return {'count': 0, 'items': []};
      }

      final queue = List<dynamic>.from(jsonDecode(queueStr));
      return {
        'count': queue.length,
        'items': queue.map((item) => {
          'id': item['id'],
          'username': item['username'],
          'timestamp': item['timestamp'],
        }).toList(),
      };
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get offline queue status: $e');
      return {'count': 0, 'items': [], 'error': e.toString()};
    }
  }

  /// Clear offline queue
  Future<void> clearOfflineQueue() async {
    await _ensureInitialized();

    try {
      await _secureStorage.delete(key: _offlineQueueKey);
      await ComprehensiveLoggingService.logInfo('🗑️ Offline queue cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to clear offline queue: $e');
    }
  }
}
