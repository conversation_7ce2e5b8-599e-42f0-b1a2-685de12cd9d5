// lib/widgets/firebase_auth_signin_modal.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/firebase_auth_controller.dart';
import '../services/user_service.dart';
import '../bulletproof/error_handler.dart';

/// Firebase-powered sign-in modal
class FirebaseAuthSignInModal extends StatefulWidget {
  final Function(String email) onSuccess;
  final VoidCallback? onBack;

  const FirebaseAuthSignInModal({
    super.key,
    required this.onSuccess,
    this.onBack,
  });

  @override
  State<FirebaseAuthSignInModal> createState() => _FirebaseAuthSignInModalState();
}

class _FirebaseAuthSignInModalState extends State<FirebaseAuthSignInModal> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isLoading = false;
  String? _error;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  bool get _canSignIn {
    return _emailController.text.isNotEmpty &&
           _passwordController.text.isNotEmpty &&
           !_isLoading;
  }

  Future<void> _handleSignIn() async {
    if (!_canSignIn) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final firebaseController = Provider.of<FirebaseAuthController>(context, listen: false);
      final input = _emailController.text.trim();

      // Determine if input is email or username
      String email = input;

      // If input doesn't contain @, treat it as username and try to find the email
      if (!input.contains('@')) {
        // Try to find user by username and get their email
        final userService = UserService(ErrorHandler());
        final user = await userService.loadUserByUsername(input);

        if (user?.email != null) {
          email = user!.email!;
        } else {
          setState(() {
            _error = 'Username not found. Please check your username or use your email address.';
          });
          return;
        }
      }

      final success = await firebaseController.signIn(
        email: email,
        password: _passwordController.text,
      );

      if (success) {
        widget.onSuccess(email);
      } else {
        setState(() {
          _error = firebaseController.error ?? 'Sign in failed';
        });
      }

    } catch (e) {
      setState(() {
        _error = 'An error occurred. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        padding: const EdgeInsets.all(32),
        child: Stack(
          children: [
            // Close button (X) - positioned absolutely in top-left with proper spacing
            if (widget.onBack != null)
              Positioned(
                top: 8,
                left: 8,
                child: GestureDetector(
                  onTap: () {
                    print('❌ Firebase Sign In close button pressed');
                    widget.onBack?.call();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white70,
                      size: 18,
                    ),
                  ),
                ),
              ),

            // Main content with top padding to avoid close button
            Padding(
              padding: const EdgeInsets.only(top: 40), // Space for close button
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                // Title - moved to top with minimal spacing
                const Text(
                  'Welcome Back',
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
            const SizedBox(height: 8),
            const Text(
              'Sign in to your MXD account',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // Error message
            if (_error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _error!,
                  style: const TextStyle(
                    color: Colors.red,
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Email field
            TextField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              autocorrect: false,
              enableSuggestions: false,
              onChanged: (value) => setState(() {}), // Trigger rebuild to update button state
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Digital-7',
                fontSize: 16,
              ),
              decoration: InputDecoration(
                labelText: 'Email or Username',
                hintText: 'Enter your email or username',
                hintStyle: const TextStyle(
                  color: Colors.white38,
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
                labelStyle: const TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.white30),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.cyanAccent),
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            // Password field
            TextField(
              controller: _passwordController,
              obscureText: true,
              textInputAction: TextInputAction.done,
              onChanged: (value) => setState(() {}), // Trigger rebuild to update button state
              onSubmitted: (_) => _handleSignIn(),
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Digital-7',
                fontSize: 16,
              ),
              decoration: InputDecoration(
                labelText: 'Password',
                labelStyle: const TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.white30),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.cyanAccent),
                ),
              ),
            ),
            const SizedBox(height: 32),
            
            // Sign in button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _canSignIn ? _handleSignIn : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _canSignIn ? Colors.cyanAccent : Colors.grey[700],
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                        ),
                      )
                    : const Text(
                        'Sign In',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Forgot password
            TextButton(
              onPressed: () {
                // TODO: Implement forgot password
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Forgot password feature coming soon!'),
                  ),
                );
              },
              child: const Text(
                'Forgot Password?',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
            ),
              ],
            ),
            ),
          ],
        ),
      ),
    );
  }
}
